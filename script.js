// 高级复利计算器主要逻辑
class CompoundCalculator {
    constructor() {
        this.calculations = [];
        this.currentCalculation = null;
        this.adjustments = new Map(); // 现在存储 "year-month" 格式的键
        this.charts = {};
        this.init();
    }

    init() {
        this.bindEvents();
        this.loadSavedCalculations();
        this.initializeTabs();
        this.loadLastCalculation();
    }

    // 绑定事件监听器
    bindEvents() {
        // 基础按钮事件
        document.getElementById('calculateBtn').addEventListener('click', () => this.calculate());
        document.getElementById('resetBtn').addEventListener('click', () => this.reset());
        
        // 年份筛选事件
        document.getElementById('yearSelect').addEventListener('change', () => {
            if (this.currentCalculation) {
                this.generateAdjustmentsTable();
            }
        });
        
        // 标签页切换事件
        document.querySelectorAll('.tab-button').forEach(button => {
            button.addEventListener('click', (e) => this.switchTab(e.target.dataset.tab));
        });

        // 保存和导出事件
        document.getElementById('saveCalculationBtn').addEventListener('click', () => this.showSaveModal());
        document.getElementById('exportBtn').addEventListener('click', () => this.exportData());
        document.getElementById('importBtn').addEventListener('click', () => this.importData());
        document.getElementById('printBtn').addEventListener('click', () => this.print());

        // 文件导入事件
        document.getElementById('importFile').addEventListener('change', (e) => this.handleFileImport(e));

        // 调整相关事件
        document.getElementById('applyAdjustmentsBtn').addEventListener('click', () => this.applyAdjustments());
        document.getElementById('clearAdjustmentsBtn').addEventListener('click', () => this.clearAdjustments());

        // 保存的计算相关事件
        document.getElementById('clearAllBtn').addEventListener('click', () => this.clearAllSaved());

        // 模态框事件
        document.getElementById('confirmSaveBtn').addEventListener('click', () => this.saveCalculation());
        document.getElementById('cancelSaveBtn').addEventListener('click', () => this.hideSaveModal());
        document.querySelector('.close').addEventListener('click', () => this.hideSaveModal());

        // 点击模态框外部关闭
        window.addEventListener('click', (e) => {
            const modal = document.getElementById('saveModal');
            if (e.target === modal) {
                this.hideSaveModal();
            }
        });
    }

    // 初始化标签页
    initializeTabs() {
        this.switchTab('results');
    }

    // 切换标签页
    switchTab(tabName) {
        // 移除所有活动状态
        document.querySelectorAll('.tab-button').forEach(btn => btn.classList.remove('active'));
        document.querySelectorAll('.tab-panel').forEach(panel => panel.classList.remove('active'));

        // 激活选中的标签页
        document.querySelector(`[data-tab="${tabName}"]`).classList.add('active');
        document.getElementById(tabName).classList.add('active');
    }

    // 获取输入参数
    getInputParameters() {
        return {
            initialAmount: parseFloat(document.getElementById('initialAmount').value) || 0,
            expectedGrowthRate: parseFloat(document.getElementById('expectedGrowthRate').value) || 0,
            investmentYears: parseInt(document.getElementById('investmentYears').value) || 0,
            withdrawalYears: parseInt(document.getElementById('withdrawalYears').value) || 0,
            expectedWithdrawal: parseFloat(document.getElementById('expectedWithdrawal').value) || 0
        };
    }

    // 验证输入参数
    validateInputs(params) {
        const errors = [];
        
        if (params.initialAmount <= 0) {
            errors.push('初始投资金额必须大于0');
        }
        
        if (params.expectedGrowthRate < 0) {
            errors.push('预期年增长率不能为负数');
        }
        
        if (params.investmentYears < 0) {
            errors.push('投资年限不能为负数');
        }
        
        if (params.withdrawalYears < 0) {
            errors.push('取款年限不能为负数');
        }
        
        if (params.expectedWithdrawal < 0) {
            errors.push('预期每年取款金额不能为负数');
        }

        if (errors.length > 0) {
            alert('输入验证失败：\n' + errors.join('\n'));
            return false;
        }
        
        return true;
    }

    // 主计算函数
    calculate() {
        const params = this.getInputParameters();
        
        if (!this.validateInputs(params)) {
            return;
        }

        this.currentCalculation = this.performCalculation(params);
        this.updateDisplay();
        this.generateAdjustmentsTable();
        this.saveAsLastCalculation(); // 自动保存为最后一次计算
        this.switchTab('results');
    }

    // 执行复利计算（按月精确计算）
    performCalculation(params) {
        const results = {
            parameters: params,
            yearlyData: [],
            monthlyData: [], // 新增月度数据
            summary: {}
        };

        let expectedBalance = params.initialAmount;
        let actualBalance = params.initialAmount;
        let totalExpectedWithdrawals = 0;
        let totalActualWithdrawals = 0;
        let totalExpectedInvestments = params.initialAmount;
        let totalActualInvestments = params.initialAmount;

        const totalYears = params.investmentYears + params.withdrawalYears;
        const expectedMonthlyRate = Math.pow(1 + params.expectedGrowthRate / 100, 1/12) - 1;

        for (let year = 1; year <= totalYears; year++) {
            const isInvestmentPhase = year <= params.investmentYears;
            const yearStartExpectedBalance = expectedBalance;
            const yearStartActualBalance = actualBalance;
            
            let yearlyExpectedWithdrawal = 0;
            let yearlyActualWithdrawal = 0;
            let yearlyAdditionalInvestment = 0;

            for (let month = 1; month <= 12; month++) {
                const monthKey = `${year}-${month}`;
                const adjustment = this.adjustments.get(monthKey) || {};
                
                // 获取当月的实际增长率
                const actualMonthlyRate = adjustment.growthRate !== undefined ?
                    (Math.pow(1 + adjustment.growthRate / 100, 1/12) - 1) : expectedMonthlyRate;

                // 每月增长
                expectedBalance *= (1 + expectedMonthlyRate);
                actualBalance *= (1 + actualMonthlyRate);

                // 处理追加投资
                if (adjustment.additionalInvestment > 0) {
                    expectedBalance += adjustment.additionalInvestment;
                    actualBalance += adjustment.additionalInvestment;
                    totalExpectedInvestments += adjustment.additionalInvestment;
                    totalActualInvestments += adjustment.additionalInvestment;
                    yearlyAdditionalInvestment += adjustment.additionalInvestment;
                }

                // 处理取款
                if (adjustment.withdrawal > 0) {
                    const expectedWithdrawalAmount = Math.min(adjustment.withdrawal, expectedBalance);
                    const actualWithdrawalAmount = Math.min(adjustment.withdrawal, actualBalance);

                    expectedBalance -= expectedWithdrawalAmount;
                    actualBalance -= actualWithdrawalAmount;
                    totalExpectedWithdrawals += expectedWithdrawalAmount;
                    totalActualWithdrawals += actualWithdrawalAmount;
                    yearlyExpectedWithdrawal += expectedWithdrawalAmount;
                    yearlyActualWithdrawal += actualWithdrawalAmount;
                }

                // 记录月度数据
                if (adjustment.additionalInvestment > 0 || adjustment.withdrawal > 0) {
                    results.monthlyData.push({
                        year: year,
                        month: month,
                        expectedBalance: expectedBalance,
                        actualBalance: actualBalance,
                        cashFlow: (adjustment.additionalInvestment || 0) - (adjustment.withdrawal || 0),
                        type: adjustment.additionalInvestment > 0 ? 'investment' : 'withdrawal'
                    });
                }
            }

            // 记录年度汇总数据
            const yearData = {
                year: year,
                phase: isInvestmentPhase ? '投资期' : '取款期',
                expectedGrowthRate: params.expectedGrowthRate,
                actualGrowthRate: params.expectedGrowthRate, // 这里可以计算年度平均值
                expectedBalance: expectedBalance,
                actualBalance: actualBalance,
                expectedWithdrawal: yearlyExpectedWithdrawal,
                actualWithdrawal: yearlyActualWithdrawal,
                additionalInvestment: yearlyAdditionalInvestment,
                difference: actualBalance - expectedBalance,
                note: '',
                yearStartBalance: yearStartActualBalance,
                yearEndBalance: actualBalance,
                totalCashFlow: yearlyAdditionalInvestment - yearlyActualWithdrawal
            };

            results.yearlyData.push(yearData);
        }

        // 计算摘要信息
        results.summary = {
            initialAmount: params.initialAmount,
            finalExpectedBalance: expectedBalance,
            finalActualBalance: actualBalance,
            difference: actualBalance - expectedBalance,
            differencePercent: expectedBalance !== 0 ? ((actualBalance - expectedBalance) / expectedBalance * 100) : 0,
            totalExpectedWithdrawals: totalExpectedWithdrawals,
            totalActualWithdrawals: totalActualWithdrawals,
            totalExpectedInvestments: totalExpectedInvestments,
            totalActualInvestments: totalActualInvestments,
            netCashFlow: totalActualInvestments - totalActualWithdrawals
        };

        return results;
    }

    // 更新显示
    updateDisplay() {
        if (!this.currentCalculation) return;

        this.updateSummary();
        this.updateTable();
        this.updateCharts();
    }

    // 更新摘要信息
    updateSummary() {
        const summary = this.currentCalculation.summary;

        document.getElementById('summaryInitial').textContent = this.formatCurrency(summary.initialAmount);
        document.getElementById('summaryExpectedFinal').textContent = this.formatCurrency(summary.finalExpectedBalance);
        document.getElementById('summaryActualFinal').textContent = this.formatCurrency(summary.finalActualBalance);
        document.getElementById('summaryDifference').textContent = this.formatCurrency(summary.difference);
        document.getElementById('summaryDifferencePercent').textContent = summary.differencePercent.toFixed(2) + '%';
        document.getElementById('summaryExpectedWithdrawals').textContent = this.formatCurrency(summary.totalExpectedWithdrawals);
        document.getElementById('summaryActualWithdrawals').textContent = this.formatCurrency(summary.totalActualWithdrawals);
        document.getElementById('summaryTotalInvestments').textContent = this.formatCurrency(summary.totalActualInvestments);
        document.getElementById('summaryNetCashFlow').textContent = this.formatCurrency(summary.netCashFlow);
    }

    // 更新详细表格
    updateTable() {
        const tbody = document.querySelector('#resultsTable tbody');
        tbody.innerHTML = '';

        this.currentCalculation.yearlyData.forEach(data => {
            const row = tbody.insertRow();
            row.innerHTML = `
                <td>${data.year}</td>
                <td><span class="phase-${data.phase === '投资期' ? 'investment' : 'withdrawal'}">${data.phase}</span></td>
                <td>${data.expectedGrowthRate.toFixed(2)}%</td>
                <td>${data.actualGrowthRate.toFixed(2)}%</td>
                <td>${this.formatCurrency(data.expectedBalance)}</td>
                <td>${this.formatCurrency(data.actualBalance)}</td>
                <td>${this.formatCurrency(data.expectedWithdrawal)}</td>
                <td>${this.formatCurrency(data.actualWithdrawal)}</td>
                <td class="${data.difference >= 0 ? 'positive' : 'negative'}">${this.formatCurrency(data.difference)}</td>
                <td>${data.note}</td>
            `;
        });
    }

    // 格式化货币
    formatCurrency(amount) {
        return new Intl.NumberFormat('en-US', {
            style: 'currency',
            currency: 'USD',
            minimumFractionDigits: 0,
            maximumFractionDigits: 0
        }).format(amount);
    }

    // 重置表单
    reset() {
        document.getElementById('initialAmount').value = '10000';
        document.getElementById('expectedGrowthRate').value = '7';
        document.getElementById('investmentYears').value = '20';
        document.getElementById('withdrawalYears').value = '15';
        document.getElementById('expectedWithdrawal').value = '5000';
        
        this.adjustments.clear();
        this.currentCalculation = null;
        
        // 清空显示
        document.querySelector('#resultsTable tbody').innerHTML = '';
        document.querySelector('#adjustmentsTable tbody').innerHTML = '';
        
        // 重置摘要
        ['summaryInitial', 'summaryExpectedFinal', 'summaryActualFinal',
         'summaryDifference', 'summaryDifferencePercent',
         'summaryExpectedWithdrawals', 'summaryActualWithdrawals',
         'summaryTotalInvestments', 'summaryNetCashFlow'].forEach(id => {
            document.getElementById(id).textContent = '$0';
        });
        document.getElementById('summaryDifferencePercent').textContent = '0%';
        
        // 销毁图表
        Object.values(this.charts).forEach(chart => {
            if (chart) chart.destroy();
        });
        this.charts = {};
    }

    // 更新图表
    updateCharts() {
        this.updateAssetChart();
        this.updateWithdrawalChart();
        this.updateCashFlowChart();
    }

    // 更新资产价值图表
    updateAssetChart() {
        const ctx = document.getElementById('assetChart').getContext('2d');

        // 销毁现有图表
        if (this.charts.assetChart) {
            this.charts.assetChart.destroy();
        }

        const years = this.currentCalculation.yearlyData.map(d => d.year);
        const expectedBalances = this.currentCalculation.yearlyData.map(d => d.expectedBalance);
        const actualBalances = this.currentCalculation.yearlyData.map(d => d.actualBalance);

        this.charts.assetChart = new Chart(ctx, {
            type: 'line',
            data: {
                labels: years,
                datasets: [{
                    label: '预期资产价值',
                    data: expectedBalances,
                    borderColor: '#4CAF50',
                    backgroundColor: 'rgba(76, 175, 80, 0.1)',
                    borderWidth: 2,
                    fill: false
                }, {
                    label: '实际资产价值',
                    data: actualBalances,
                    borderColor: '#2196F3',
                    backgroundColor: 'rgba(33, 150, 243, 0.1)',
                    borderWidth: 2,
                    fill: false
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                scales: {
                    y: {
                        beginAtZero: true,
                        ticks: {
                            callback: function(value) {
                                return new Intl.NumberFormat('en-US', {
                                    style: 'currency',
                                    currency: 'USD',
                                    minimumFractionDigits: 0
                                }).format(value);
                            }
                        }
                    }
                },
                plugins: {
                    tooltip: {
                        callbacks: {
                            label: function(context) {
                                return context.dataset.label + ': ' +
                                    new Intl.NumberFormat('en-US', {
                                        style: 'currency',
                                        currency: 'USD',
                                        minimumFractionDigits: 0
                                    }).format(context.parsed.y);
                            }
                        }
                    }
                }
            }
        });
    }

    // 更新取款金额图表
    updateWithdrawalChart() {
        const ctx = document.getElementById('withdrawalChart').getContext('2d');

        // 销毁现有图表
        if (this.charts.withdrawalChart) {
            this.charts.withdrawalChart.destroy();
        }

        const withdrawalData = this.currentCalculation.yearlyData.filter(d => d.expectedWithdrawal > 0);
        const years = withdrawalData.map(d => d.year);
        const expectedWithdrawals = withdrawalData.map(d => d.expectedWithdrawal);
        const actualWithdrawals = withdrawalData.map(d => d.actualWithdrawal);

        this.charts.withdrawalChart = new Chart(ctx, {
            type: 'bar',
            data: {
                labels: years,
                datasets: [{
                    label: '预期取款金额',
                    data: expectedWithdrawals,
                    backgroundColor: 'rgba(76, 175, 80, 0.7)',
                    borderColor: '#4CAF50',
                    borderWidth: 1
                }, {
                    label: '实际取款金额',
                    data: actualWithdrawals,
                    backgroundColor: 'rgba(33, 150, 243, 0.7)',
                    borderColor: '#2196F3',
                    borderWidth: 1
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                scales: {
                    y: {
                        beginAtZero: true,
                        ticks: {
                            callback: function(value) {
                                return new Intl.NumberFormat('en-US', {
                                    style: 'currency',
                                    currency: 'USD',
                                    minimumFractionDigits: 0
                                }).format(value);
                            }
                        }
                    }
                },
                plugins: {
                    tooltip: {
                        callbacks: {
                            label: function(context) {
                                return context.dataset.label + ': ' +
                                    new Intl.NumberFormat('en-US', {
                                        style: 'currency',
                                        currency: 'USD',
                                        minimumFractionDigits: 0
                                    }).format(context.parsed.y);
                            }
                        }
                    }
                }
            }
        });
    }

    // 更新现金流图表
    updateCashFlowChart() {
        const ctx = document.getElementById('cashFlowChart').getContext('2d');

        // 销毁现有图表
        if (this.charts.cashFlowChart) {
            this.charts.cashFlowChart.destroy();
        }

        const years = this.currentCalculation.yearlyData.map(d => d.year);
        const investments = this.currentCalculation.yearlyData.map(d => d.additionalInvestment || 0);
        const withdrawals = this.currentCalculation.yearlyData.map(d => -(d.actualWithdrawal || 0));

        this.charts.cashFlowChart = new Chart(ctx, {
            type: 'bar',
            data: {
                labels: years,
                datasets: [{
                    label: '追加投资',
                    data: investments,
                    backgroundColor: 'rgba(76, 175, 80, 0.7)',
                    borderColor: '#4CAF50',
                    borderWidth: 1
                }, {
                    label: '取款',
                    data: withdrawals,
                    backgroundColor: 'rgba(244, 67, 54, 0.7)',
                    borderColor: '#f44336',
                    borderWidth: 1
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                scales: {
                    y: {
                        ticks: {
                            callback: function(value) {
                                return new Intl.NumberFormat('en-US', {
                                    style: 'currency',
                                    currency: 'USD',
                                    minimumFractionDigits: 0
                                }).format(Math.abs(value));
                            }
                        }
                    }
                },
                plugins: {
                    tooltip: {
                        callbacks: {
                            label: function(context) {
                                const value = Math.abs(context.parsed.y);
                                const type = context.parsed.y >= 0 ? '投入' : '取出';
                                return `${context.dataset.label}: ${type} ` +
                                    new Intl.NumberFormat('en-US', {
                                        style: 'currency',
                                        currency: 'USD',
                                        minimumFractionDigits: 0
                                    }).format(value);
                            }
                        }
                    }
                }
            }
        });
    }

    // 生成逐月调整表格
    generateAdjustmentsTable() {
        const tbody = document.querySelector('#adjustmentsTable tbody');
        const yearSelect = document.getElementById('yearSelect');
        tbody.innerHTML = '';
        
        // 更新年份选择器
        yearSelect.innerHTML = '<option value="all">显示所有年份</option>';

        const params = this.currentCalculation.parameters;
        const totalYears = params.investmentYears + params.withdrawalYears;
        const selectedYear = yearSelect.value;

        // 添加年份选项
        for (let year = 1; year <= totalYears; year++) {
            const option = document.createElement('option');
            option.value = year;
            option.textContent = `第${year}年`;
            yearSelect.appendChild(option);
        }

        for (let year = 1; year <= totalYears; year++) {
            if (selectedYear !== 'all' && selectedYear != year) continue;
            
            const isInvestmentPhase = year <= params.investmentYears;
            
            for (let month = 1; month <= 12; month++) {
                const monthKey = `${year}-${month}`;
                const adjustment = this.adjustments.get(monthKey) || {};

                const row = tbody.insertRow();
                row.innerHTML = `
                    <td>${year}</td>
                    <td>${month}</td>
                    <td><span class="phase-${isInvestmentPhase ? 'investment' : 'withdrawal'}">${isInvestmentPhase ? '投资期' : '取款期'}</span></td>
                    <td><input type="number" step="0.01" min="0" value="${adjustment.growthRate || ''}"
                        onchange="calculator.updateMonthlyAdjustment(${year}, ${month}, 'growthRate', this.value)"
                        placeholder="${params.expectedGrowthRate}%"></td>
                    <td><input type="number" step="0.01" min="0" value="${adjustment.withdrawal || ''}"
                        onchange="calculator.updateMonthlyAdjustment(${year}, ${month}, 'withdrawal', this.value)"
                        placeholder="0"></td>
                    <td><input type="number" step="0.01" min="0" value="${adjustment.additionalInvestment || ''}"
                        onchange="calculator.updateMonthlyAdjustment(${year}, ${month}, 'additionalInvestment', this.value)"
                        placeholder="0"></td>
                    <td><input type="text" value="${adjustment.note || ''}"
                        onchange="calculator.updateMonthlyAdjustment(${year}, ${month}, 'note', this.value)"
                        placeholder="备注信息"></td>
                `;
            }
        }
    }

    // 更新月度调整数据
    updateMonthlyAdjustment(year, month, field, value) {
        const monthKey = `${year}-${month}`;
        
        if (!this.adjustments.has(monthKey)) {
            this.adjustments.set(monthKey, {});
        }

        const adjustment = this.adjustments.get(monthKey);

        if (field === 'note') {
            adjustment[field] = value;
        } else {
            const numValue = parseFloat(value);
            adjustment[field] = isNaN(numValue) ? undefined : numValue;
        }

        // 如果所有字段都为空，删除这个调整
        if (Object.values(adjustment).every(v => v === undefined || v === '')) {
            this.adjustments.delete(monthKey);
        }
    }

    // 应用调整
    applyAdjustments() {
        if (this.currentCalculation) {
            this.currentCalculation = this.performCalculation(this.currentCalculation.parameters);
            this.updateDisplay();
            alert('调整已应用！');
        }
    }

    // 清除调整
    clearAdjustments() {
        if (confirm('确定要清除所有调整吗？')) {
            this.adjustments.clear();
            this.generateAdjustmentsTable();
            if (this.currentCalculation) {
                this.currentCalculation = this.performCalculation(this.currentCalculation.parameters);
                this.updateDisplay();
            }
        }
    }

    // 显示保存模态框
    showSaveModal() {
        if (!this.currentCalculation) {
            alert('请先进行计算！');
            return;
        }
        document.getElementById('saveModal').style.display = 'block';
        document.getElementById('calculationName').value = '';
        document.getElementById('calculationName').focus();
    }

    // 隐藏保存模态框
    hideSaveModal() {
        document.getElementById('saveModal').style.display = 'none';
    }

    // 保存计算
    saveCalculation() {
        const name = document.getElementById('calculationName').value.trim();
        if (!name) {
            alert('请输入计算名称！');
            return;
        }

        const calculationData = {
            id: Date.now().toString(),
            name: name,
            timestamp: new Date().toISOString(),
            parameters: this.currentCalculation.parameters,
            adjustments: Object.fromEntries(this.adjustments),
            results: this.currentCalculation
        };

        this.calculations.push(calculationData);
        this.saveToLocalStorage();
        this.updateSavedCalculationsList();
        this.hideSaveModal();
        alert('计算已保存！');
    }

    // 保存到本地存储
    saveToLocalStorage() {
        try {
            localStorage.setItem('compoundCalculatorData', JSON.stringify(this.calculations));
        } catch (error) {
            alert('保存失败：' + error.message);
        }
    }

    // 从本地存储加载
    loadSavedCalculations() {
        try {
            const saved = localStorage.getItem('compoundCalculatorData');
            if (saved) {
                this.calculations = JSON.parse(saved);
                this.updateSavedCalculationsList();
            }
        } catch (error) {
            console.error('加载保存的计算失败：', error);
            this.calculations = [];
        }
    }

    // 加载最后一次计算
    loadLastCalculation() {
        try {
            const lastCalculation = localStorage.getItem('lastCalculation');
            if (lastCalculation) {
                const data = JSON.parse(lastCalculation);

                // 恢复参数到表单
                document.getElementById('initialAmount').value = data.parameters.initialAmount;
                document.getElementById('expectedGrowthRate').value = data.parameters.expectedGrowthRate;
                document.getElementById('investmentYears').value = data.parameters.investmentYears;
                document.getElementById('withdrawalYears').value = data.parameters.withdrawalYears;
                document.getElementById('expectedWithdrawal').value = data.parameters.expectedWithdrawal;

                // 恢复调整数据
                this.adjustments.clear();
                if (data.adjustments) {
                    Object.entries(data.adjustments).forEach(([year, adjustment]) => {
                        this.adjustments.set(parseInt(year), adjustment);
                    });
                }

                // 重新计算并显示
                this.currentCalculation = this.performCalculation(data.parameters);
                this.updateDisplay();
                this.generateAdjustmentsTable();

                console.log('已自动加载上次的计算数据');
            }
        } catch (error) {
            console.error('加载最后一次计算失败：', error);
        }
    }

    // 保存当前计算为最后一次计算
    saveAsLastCalculation() {
        if (this.currentCalculation) {
            try {
                const data = {
                    parameters: this.currentCalculation.parameters,
                    adjustments: Object.fromEntries(this.adjustments),
                    timestamp: new Date().toISOString()
                };
                localStorage.setItem('lastCalculation', JSON.stringify(data));
            } catch (error) {
                console.error('保存最后一次计算失败：', error);
            }
        }
    }

    // 更新保存的计算列表
    updateSavedCalculationsList() {
        const container = document.getElementById('savedCalculations');

        if (this.calculations.length === 0) {
            container.innerHTML = '<p class="no-data">暂无保存的计算记录</p>';
            return;
        }

        container.innerHTML = this.calculations.map(calc => `
            <div class="saved-calculation">
                <h4>${calc.name}</h4>
                <p>保存时间: ${new Date(calc.timestamp).toLocaleString('zh-CN')}</p>
                <p>初始投资: ${this.formatCurrency(calc.parameters.initialAmount)} |
                   投资年限: ${calc.parameters.investmentYears}年 |
                   取款年限: ${calc.parameters.withdrawalYears}年</p>
                <div class="button-group">
                    <button class="btn btn-primary" onclick="calculator.loadCalculation('${calc.id}')">加载</button>
                    <button class="btn btn-danger" onclick="calculator.deleteCalculation('${calc.id}')">删除</button>
                </div>
            </div>
        `).join('');
    }

    // 加载计算
    loadCalculation(id) {
        const calculation = this.calculations.find(c => c.id === id);
        if (!calculation) {
            alert('计算记录不存在！');
            return;
        }

        // 恢复参数
        const params = calculation.parameters;
        document.getElementById('initialAmount').value = params.initialAmount;
        document.getElementById('expectedGrowthRate').value = params.expectedGrowthRate;
        document.getElementById('investmentYears').value = params.investmentYears;
        document.getElementById('withdrawalYears').value = params.withdrawalYears;
        document.getElementById('expectedWithdrawal').value = params.expectedWithdrawal;

        // 恢复调整
        this.adjustments.clear();
        if (calculation.adjustments) {
            Object.entries(calculation.adjustments).forEach(([year, adjustment]) => {
                this.adjustments.set(parseInt(year), adjustment);
            });
        }

        // 重新计算
        this.calculate();
        alert('计算记录已加载！');
    }

    // 删除计算
    deleteCalculation(id) {
        if (confirm('确定要删除这个计算记录吗？')) {
            this.calculations = this.calculations.filter(c => c.id !== id);
            this.saveToLocalStorage();
            this.updateSavedCalculationsList();
        }
    }

    // 清除所有保存的数据
    clearAllSaved() {
        if (confirm('确定要清除所有保存的计算记录吗？此操作不可撤销！')) {
            this.calculations = [];
            localStorage.removeItem('compoundCalculatorData');
            this.updateSavedCalculationsList();
            alert('所有数据已清除！');
        }
    }

    // 导出数据
    exportData() {
        if (!this.currentCalculation) {
            alert('请先进行计算！');
            return;
        }

        // 询问用户导出格式
        const format = confirm('选择导出格式：\n\n确定 = JSON格式（可导入）\n取消 = 文本格式（可读性好）');

        if (format) {
            this.exportAsJSON();
        } else {
            this.exportAsText();
        }
    }

    // 导出为JSON格式
    exportAsJSON() {
        const data = {
            exportType: 'compoundCalculator',
            version: '1.0',
            timestamp: new Date().toISOString(),
            parameters: this.currentCalculation.parameters,
            adjustments: Object.fromEntries(this.adjustments),
            results: this.currentCalculation
        };

        const jsonString = JSON.stringify(data, null, 2);
        const blob = new Blob([jsonString], { type: 'application/json;charset=utf-8' });
        const filename = `复利计算数据_${new Date().toISOString().slice(0, 10)}.json`;

        this.downloadFile(blob, filename);
    }

    // 导出为文本格式
    exportAsText() {
        const data = this.generateExportData();
        const blob = new Blob([data], { type: 'text/plain;charset=utf-8' });
        const filename = `复利计算结果_${new Date().toISOString().slice(0, 10)}.txt`;

        this.downloadFile(blob, filename);
    }

    // 下载文件的通用方法
    downloadFile(blob, filename) {
        if (typeof saveAs !== 'undefined') {
            saveAs(blob, filename);
        } else {
            // 备用方案
            const url = URL.createObjectURL(blob);
            const a = document.createElement('a');
            a.href = url;
            a.download = filename;
            document.body.appendChild(a);
            a.click();
            document.body.removeChild(a);
            URL.revokeObjectURL(url);
        }
    }

    // 生成导出数据
    generateExportData() {
        const calc = this.currentCalculation;
        const params = calc.parameters;
        const summary = calc.summary;

        let data = '高级复利计算器 - 计算结果\n';
        data += '==================================================\n\n';

        // 基础参数
        data += '基础参数:\n';
        data += '--------------------\n';
        data += `初始投资金额: ${this.formatCurrency(params.initialAmount)}\n`;
        data += `预期年增长率: ${params.expectedGrowthRate}%\n`;
        data += `投资年限: ${params.investmentYears}年\n`;
        data += `取款年限: ${params.withdrawalYears}年\n`;
        data += `预期每年取款金额: ${this.formatCurrency(params.expectedWithdrawal)}\n\n`;

        // 摘要信息
        data += '摘要信息:\n';
        data += '--------------------\n';
        data += `初始投资金额: ${this.formatCurrency(summary.initialAmount)}\n`;
        data += `最终预期余额: ${this.formatCurrency(summary.finalExpectedBalance)}\n`;
        data += `最终实际余额: ${this.formatCurrency(summary.finalActualBalance)}\n`;
        data += `差异金额: ${this.formatCurrency(summary.difference)}\n`;
        data += `差异百分比: ${summary.differencePercent.toFixed(2)}%\n`;
        data += `总预期取款: ${this.formatCurrency(summary.totalExpectedWithdrawals)}\n`;
        data += `总实际取款: ${this.formatCurrency(summary.totalActualWithdrawals)}\n\n`;

        // 详细数据表格
        data += '详细数据:\n';
        data += '--------------------\n';
        data += '年份\t阶段\t预期增长率\t实际增长率\t预期资产价值\t实际资产价值\t预期取款\t实际取款\t差异\t备注\n';

        calc.yearlyData.forEach(row => {
            data += `${row.year}\t${row.phase}\t${row.expectedGrowthRate.toFixed(2)}%\t${row.actualGrowthRate.toFixed(2)}%\t`;
            data += `${this.formatCurrency(row.expectedBalance)}\t${this.formatCurrency(row.actualBalance)}\t`;
            data += `${this.formatCurrency(row.expectedWithdrawal)}\t${this.formatCurrency(row.actualWithdrawal)}\t`;
            data += `${this.formatCurrency(row.difference)}\t${row.note}\n`;
        });

        data += '\n生成时间: ' + new Date().toLocaleString('zh-CN');

        return data;
    }

    // 打印
    print() {
        if (!this.currentCalculation) {
            alert('请先进行计算！');
            return;
        }

        // 添加打印样式类
        document.body.classList.add('printing');

        // 延迟执行打印，确保样式生效
        setTimeout(() => {
            window.print();
            document.body.classList.remove('printing');
        }, 100);
    }

    // 导入数据
    importData() {
        document.getElementById('importFile').click();
    }

    // 处理文件导入
    handleFileImport(event) {
        const file = event.target.files[0];
        if (!file) return;

        const reader = new FileReader();
        reader.onload = (e) => {
            try {
                const content = e.target.result;
                this.parseImportedData(content);
            } catch (error) {
                alert('文件读取失败：' + error.message);
            }
        };
        reader.readAsText(file, 'UTF-8');

        // 清空文件输入，允许重复选择同一文件
        event.target.value = '';
    }

    // 解析导入的数据
    parseImportedData(content) {
        try {
            // 尝试解析JSON格式（新格式）
            if (content.trim().startsWith('{')) {
                this.importFromJSON(content);
                return;
            }

            // 解析文本格式（导出的txt文件）
            this.importFromText(content);

        } catch (error) {
            alert('数据解析失败：' + error.message + '\n\n请确保导入的是有效的计算器数据文件。');
        }
    }

    // 从JSON格式导入
    importFromJSON(content) {
        const data = JSON.parse(content);

        if (data.parameters) {
            // 恢复参数
            document.getElementById('initialAmount').value = data.parameters.initialAmount || 10000;
            document.getElementById('expectedGrowthRate').value = data.parameters.expectedGrowthRate || 7;
            document.getElementById('investmentYears').value = data.parameters.investmentYears || 20;
            document.getElementById('withdrawalYears').value = data.parameters.withdrawalYears || 15;
            document.getElementById('expectedWithdrawal').value = data.parameters.expectedWithdrawal || 5000;

            // 恢复调整
            this.adjustments.clear();
            if (data.adjustments) {
                Object.entries(data.adjustments).forEach(([year, adjustment]) => {
                    this.adjustments.set(parseInt(year), adjustment);
                });
            }

            // 重新计算
            this.calculate();
            alert('数据导入成功！');
        } else {
            throw new Error('无效的JSON数据格式');
        }
    }

    // 从文本格式导入（解析导出的txt文件）
    importFromText(content) {
        const lines = content.split('\n');
        const params = {};

        // 解析基础参数
        for (const line of lines) {
            if (line.includes('初始投资金额:')) {
                const match = line.match(/\$([0-9,]+)/);
                if (match) params.initialAmount = parseFloat(match[1].replace(/,/g, ''));
            } else if (line.includes('预期年增长率:')) {
                const match = line.match(/([0-9.]+)%/);
                if (match) params.expectedGrowthRate = parseFloat(match[1]);
            } else if (line.includes('投资年限:')) {
                const match = line.match(/([0-9]+)年/);
                if (match) params.investmentYears = parseInt(match[1]);
            } else if (line.includes('取款年限:')) {
                const match = line.match(/([0-9]+)年/);
                if (match) params.withdrawalYears = parseInt(match[1]);
            } else if (line.includes('预期每年取款金额:')) {
                const match = line.match(/\$([0-9,]+)/);
                if (match) params.expectedWithdrawal = parseFloat(match[1].replace(/,/g, ''));
            }
        }

        // 验证必要参数
        if (!params.initialAmount || !params.expectedGrowthRate ||
            params.investmentYears === undefined || params.withdrawalYears === undefined) {
            throw new Error('文件中缺少必要的参数信息');
        }

        // 恢复参数到表单
        document.getElementById('initialAmount').value = params.initialAmount;
        document.getElementById('expectedGrowthRate').value = params.expectedGrowthRate;
        document.getElementById('investmentYears').value = params.investmentYears;
        document.getElementById('withdrawalYears').value = params.withdrawalYears;
        document.getElementById('expectedWithdrawal').value = params.expectedWithdrawal || 0;

        // 清除调整（文本格式不包含调整信息）
        this.adjustments.clear();

        // 重新计算
        this.calculate();
        alert('数据导入成功！\n注意：文本格式导入不包含调整信息，如需要请手动设置。');
    }
}

// 页面加载完成后初始化
document.addEventListener('DOMContentLoaded', () => {
    window.calculator = new CompoundCalculator();
});
