<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>高级复利计算器 - 测试页面</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .test-section {
            background: white;
            padding: 20px;
            margin: 20px 0;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .test-result {
            padding: 10px;
            margin: 10px 0;
            border-radius: 4px;
        }
        .success {
            background-color: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .error {
            background-color: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        .info {
            background-color: #d1ecf1;
            color: #0c5460;
            border: 1px solid #bee5eb;
        }
        button {
            background-color: #4CAF50;
            color: white;
            padding: 10px 20px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background-color: #45a049;
        }
        .main-app-link {
            display: block;
            text-align: center;
            margin: 20px 0;
            padding: 15px;
            background-color: #2196F3;
            color: white;
            text-decoration: none;
            border-radius: 8px;
            font-size: 18px;
            font-weight: bold;
        }
        .main-app-link:hover {
            background-color: #1976D2;
        }
    </style>
</head>
<body>
    <h1>高级复利计算器 - 功能测试</h1>
    
    <a href="index.html" class="main-app-link">🚀 打开主应用</a>
    
    <div class="test-section">
        <h2>📋 功能检查清单</h2>
        <div id="checklistResults">
            <div class="test-result info">正在检查功能...</div>
        </div>
        <button onclick="runFunctionalTests()">运行功能测试</button>
    </div>

    <div class="test-section">
        <h2>🧮 计算逻辑测试</h2>
        <div id="calculationResults">
            <div class="test-result info">点击按钮开始计算测试</div>
        </div>
        <button onclick="runCalculationTests()">运行计算测试</button>
        <button onclick="testMonthlyCalculation()">测试按月计算</button>
    </div>

    <div class="test-section">
        <h2>💾 存储功能测试</h2>
        <div id="storageResults">
            <div class="test-result info">点击按钮开始存储测试</div>
        </div>
        <button onclick="runStorageTests()">运行存储测试</button>
        <button onclick="testAutoLoad()">测试自动加载</button>
        <button onclick="clearTestData()">清除测试数据</button>
    </div>

    <div class="test-section">
        <h2>📥📤 导入导出功能测试</h2>
        <div id="importExportResults">
            <div class="test-result info">点击按钮测试导入导出功能</div>
        </div>
        <button onclick="testExportImport()">测试导出导入</button>
        <button onclick="testJSONFormat()">测试JSON格式</button>
    </div>

    <div class="test-section">
        <h2>📊 图表库测试</h2>
        <div id="chartResults">
            <div class="test-result info">点击按钮测试图表库</div>
        </div>
        <button onclick="runChartTests()">测试图表库</button>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/file-saver@2.0.5/dist/FileSaver.min.js"></script>
    <script>
        function addResult(containerId, message, type = 'info') {
            const container = document.getElementById(containerId);
            const div = document.createElement('div');
            div.className = `test-result ${type}`;
            div.textContent = message;
            container.appendChild(div);
        }

        function clearResults(containerId) {
            document.getElementById(containerId).innerHTML = '';
        }

        function runFunctionalTests() {
            clearResults('checklistResults');
            
            const tests = [
                { name: 'HTML页面结构', test: () => document.querySelector('.container') !== null },
                { name: 'CSS样式加载', test: () => getComputedStyle(document.body).fontFamily.includes('Arial') },
                { name: 'Chart.js库', test: () => typeof Chart !== 'undefined' },
                { name: 'FileSaver.js库', test: () => typeof saveAs !== 'undefined' },
                { name: 'localStorage支持', test: () => typeof Storage !== 'undefined' },
                { name: 'JSON支持', test: () => typeof JSON !== 'undefined' },
                { name: '现代JavaScript支持', test: () => typeof Map !== 'undefined' && typeof Set !== 'undefined' }
            ];

            tests.forEach(test => {
                try {
                    const result = test.test();
                    addResult('checklistResults', 
                        `✓ ${test.name}: ${result ? '通过' : '失败'}`, 
                        result ? 'success' : 'error'
                    );
                } catch (error) {
                    addResult('checklistResults', 
                        `✗ ${test.name}: 错误 - ${error.message}`, 
                        'error'
                    );
                }
            });
        }

        function runCalculationTests() {
            clearResults('calculationResults');
            
            try {
                // 测试基础复利计算
                const testParams = {
                    initialAmount: 10000,
                    expectedGrowthRate: 7,
                    investmentYears: 5,
                    withdrawalYears: 0,
                    expectedWithdrawal: 0
                };

                // 手动计算预期结果
                const expectedFinal = 10000 * Math.pow(1.07, 5);
                addResult('calculationResults', 
                    `测试参数: 初始$10,000, 7%增长率, 5年投资期`, 
                    'info'
                );
                addResult('calculationResults', 
                    `预期最终金额: $${expectedFinal.toFixed(2)}`, 
                    'success'
                );

                // 测试取款逻辑
                const withdrawalTest = {
                    balance: 50000,
                    withdrawal: 60000
                };
                const actualWithdrawal = Math.min(withdrawalTest.withdrawal, withdrawalTest.balance);
                addResult('calculationResults', 
                    `取款限制测试: 余额$50,000, 尝试取款$60,000, 实际取款$${actualWithdrawal}`, 
                    actualWithdrawal === 50000 ? 'success' : 'error'
                );

            } catch (error) {
                addResult('calculationResults',
                    `计算测试失败: ${error.message}`,
                    'error'
                );
            }
        }

        function testMonthlyCalculation() {
            clearResults('calculationResults');

            try {
                // 测试按月计算逻辑
                const yearlyRate = 12; // 12%年增长率
                const monthlyRate = Math.pow(1 + yearlyRate / 100, 1/12) - 1;

                addResult('calculationResults',
                    `年增长率: ${yearlyRate}%`,
                    'info'
                );
                addResult('calculationResults',
                    `月增长率: ${(monthlyRate * 100).toFixed(4)}%`,
                    'info'
                );

                // 验证：12个月的复合增长应该等于年增长率
                const compoundedMonthly = Math.pow(1 + monthlyRate, 12) - 1;
                const difference = Math.abs(compoundedMonthly - yearlyRate / 100);

                if (difference < 0.0001) {
                    addResult('calculationResults',
                        `✓ 月度复合增长率验证通过: ${(compoundedMonthly * 100).toFixed(4)}%`,
                        'success'
                    );
                } else {
                    addResult('calculationResults',
                        `✗ 月度复合增长率验证失败: 差异 ${(difference * 100).toFixed(4)}%`,
                        'error'
                    );
                }

                // 测试年中追加投资的影响
                const initialAmount = 10000;
                const additionalInvestment = 5000;

                // 方案1：年初投入15000，享受全年增长
                const fullYearGrowth = 15000 * (1 + yearlyRate / 100);

                // 方案2：年初10000，年中追加5000（享受半年增长）
                const halfYearGrowth = (10000 * Math.pow(1 + monthlyRate, 12)) +
                                      (5000 * Math.pow(1 + monthlyRate, 6));

                addResult('calculationResults',
                    `全年投入15000的结果: ${fullYearGrowth.toFixed(2)}`,
                    'info'
                );
                addResult('calculationResults',
                    `年中追加5000的结果: ${halfYearGrowth.toFixed(2)}`,
                    'info'
                );
                addResult('calculationResults',
                    `差异: ${(fullYearGrowth - halfYearGrowth).toFixed(2)} (年中追加较少)`,
                    halfYearGrowth < fullYearGrowth ? 'success' : 'error'
                );

            } catch (error) {
                addResult('calculationResults',
                    `按月计算测试失败: ${error.message}`,
                    'error'
                );
            }
        }

        function runStorageTests() {
            clearResults('storageResults');
            
            try {
                // 测试localStorage写入
                const testData = { test: 'data', timestamp: Date.now() };
                localStorage.setItem('calculatorTest', JSON.stringify(testData));
                addResult('storageResults', '✓ localStorage写入测试通过', 'success');

                // 测试localStorage读取
                const retrieved = JSON.parse(localStorage.getItem('calculatorTest'));
                if (retrieved && retrieved.test === 'data') {
                    addResult('storageResults', '✓ localStorage读取测试通过', 'success');
                } else {
                    addResult('storageResults', '✗ localStorage读取测试失败', 'error');
                }

                // 测试数据序列化
                const complexData = {
                    calculations: [{ id: '1', name: 'test' }],
                    adjustments: { 1: { growthRate: 5 } }
                };
                const serialized = JSON.stringify(complexData);
                const deserialized = JSON.parse(serialized);
                
                if (deserialized.calculations[0].name === 'test') {
                    addResult('storageResults', '✓ 复杂数据序列化测试通过', 'success');
                } else {
                    addResult('storageResults', '✗ 复杂数据序列化测试失败', 'error');
                }

            } catch (error) {
                addResult('storageResults', 
                    `存储测试失败: ${error.message}`, 
                    'error'
                );
            }
        }

        function runChartTests() {
            clearResults('chartResults');
            
            try {
                if (typeof Chart === 'undefined') {
                    addResult('chartResults', '✗ Chart.js库未加载', 'error');
                    return;
                }

                addResult('chartResults', '✓ Chart.js库已加载', 'success');
                addResult('chartResults', `Chart.js版本: ${Chart.version || '未知'}`, 'info');

                // 测试图表类型支持
                const supportedTypes = ['line', 'bar', 'pie', 'doughnut'];
                supportedTypes.forEach(type => {
                    try {
                        Chart.registry.getController(type);
                        addResult('chartResults', `✓ 支持${type}图表`, 'success');
                    } catch (e) {
                        addResult('chartResults', `✗ 不支持${type}图表`, 'error');
                    }
                });

            } catch (error) {
                addResult('chartResults', 
                    `图表测试失败: ${error.message}`, 
                    'error'
                );
            }
        }

        function clearTestData() {
            try {
                localStorage.removeItem('calculatorTest');
                localStorage.removeItem('compoundCalculatorData');
                localStorage.removeItem('lastCalculation');
                addResult('storageResults', '✓ 测试数据已清除', 'success');
            } catch (error) {
                addResult('storageResults',
                    `清除数据失败: ${error.message}`,
                    'error'
                );
            }
        }

        function testAutoLoad() {
            clearResults('storageResults');

            try {
                // 模拟保存最后一次计算
                const testCalculation = {
                    parameters: {
                        initialAmount: 15000,
                        expectedGrowthRate: 8,
                        investmentYears: 10,
                        withdrawalYears: 5,
                        expectedWithdrawal: 3000
                    },
                    adjustments: {
                        1: { growthRate: 9, note: '测试调整' }
                    },
                    timestamp: new Date().toISOString()
                };

                localStorage.setItem('lastCalculation', JSON.stringify(testCalculation));
                addResult('storageResults', '✓ 测试数据已保存到lastCalculation', 'success');
                addResult('storageResults', '💡 请刷新主页面查看自动加载效果', 'info');

            } catch (error) {
                addResult('storageResults',
                    `自动加载测试失败: ${error.message}`,
                    'error'
                );
            }
        }

        function testExportImport() {
            clearResults('importExportResults');

            try {
                // 测试JSON格式数据结构
                const testData = {
                    exportType: 'compoundCalculator',
                    version: '1.0',
                    timestamp: new Date().toISOString(),
                    parameters: {
                        initialAmount: 20000,
                        expectedGrowthRate: 6.5,
                        investmentYears: 15,
                        withdrawalYears: 10,
                        expectedWithdrawal: 4000
                    },
                    adjustments: {
                        1: { growthRate: 7, note: '第一年调整' },
                        5: { additionalInvestment: 5000, note: '追加投资' }
                    }
                };

                const jsonString = JSON.stringify(testData, null, 2);
                addResult('importExportResults', '✓ JSON数据结构验证通过', 'success');
                addResult('importExportResults', `数据大小: ${jsonString.length} 字符`, 'info');

                // 测试解析
                const parsed = JSON.parse(jsonString);
                if (parsed.parameters && parsed.adjustments) {
                    addResult('importExportResults', '✓ JSON解析测试通过', 'success');
                } else {
                    addResult('importExportResults', '✗ JSON解析测试失败', 'error');
                }

            } catch (error) {
                addResult('importExportResults',
                    `导入导出测试失败: ${error.message}`,
                    'error'
                );
            }
        }

        function testJSONFormat() {
            clearResults('importExportResults');

            try {
                // 测试文本格式解析
                const textData = `高级复利计算器 - 计算结果
==================================================

基础参数:
--------------------
初始投资金额: $25,000
预期年增长率: 7.5%
投资年限: 12年
取款年限: 8年
预期每年取款金额: $6,000`;

                addResult('importExportResults', '✓ 文本格式数据准备完成', 'success');

                // 测试参数提取
                const lines = textData.split('\n');
                let foundParams = 0;

                for (const line of lines) {
                    if (line.includes('初始投资金额:')) foundParams++;
                    if (line.includes('预期年增长率:')) foundParams++;
                    if (line.includes('投资年限:')) foundParams++;
                    if (line.includes('取款年限:')) foundParams++;
                    if (line.includes('预期每年取款金额:')) foundParams++;
                }

                if (foundParams === 5) {
                    addResult('importExportResults', '✓ 文本格式参数提取测试通过', 'success');
                } else {
                    addResult('importExportResults', `✗ 文本格式参数提取不完整 (${foundParams}/5)`, 'error');
                }

                addResult('importExportResults', '💡 可以在主页面测试实际的导入导出功能', 'info');

            } catch (error) {
                addResult('importExportResults',
                    `格式测试失败: ${error.message}`,
                    'error'
                );
            }
        }

        // 页面加载时自动运行基础检查
        document.addEventListener('DOMContentLoaded', () => {
            setTimeout(runFunctionalTests, 500);
        });
    </script>
</body>
</html>
