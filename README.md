# 高级复利计算器

一款功能全面的复利计算器，支持用户进行长期投资规划，对比预期与实际投资表现，并提供详细的数据分析和可视化功能。

## 功能特性

### 🧮 基础计算功能
- 支持初始投资金额、预期年增长率、投资年限、取款年限等参数设置
- **按月精确计算**：年增长率转换为月增长率，确保追加投资的增长时间准确
- 精确的复利计算公式：A = P(1 + r)^t
- **灵活的现金流管理**：投资期和取款期都可以进行追加投资或取款
- 智能限制取款金额不超过当前资产余额

### ⚙️ 高级调整功能
- 逐年调整实际增长率（覆盖默认预期值）
- **任意年份设置取款金额**：不再限制于取款期
- **任意年份追加投资**：不再限制于投资期
- 每项调整支持添加备注说明
- 表格支持行内编辑，操作简便
- **智能时机处理**：追加投资假设在年中，取款假设在年末

### 📊 数据展示功能
- **摘要信息**：显示初始投资、最终余额、差异分析、总投入、净现金流等关键指标
- **详细表格**：逐年显示预期和实际数据对比
- **可视化图表**：
  - 预期与实际资产价值趋势图
  - 预期与实际取款金额对比柱状图
  - **现金流分析图**：直观显示每年的追加投资和取款情况
  - 交互式图表工具提示

### 💾 数据管理功能
- 使用localStorage进行本地数据存储
- 支持计算记录命名和时间戳记录
- 保存/加载历史计算记录
- 删除单个或清除所有保存数据

### 📤 导出与打印功能
- **智能导出**：支持JSON格式（可导入）和文本格式（可读性好）
- **数据导入**：支持导入之前导出的JSON或文本文件
- **自动加载**：页面打开时自动加载上次的计算数据
- 包含完整的摘要和详细表格数据
- 打印优化布局，隐藏操作元素
- 自动调整字体大小适合打印

### 🎨 用户界面功能
- 现代化的标签页导航设计
- 响应式布局，适配不同屏幕尺寸
- 移动设备友好的操作体验
- 直观的操作流程和清晰的错误提示

## 技术栈

- **前端**: HTML5, CSS3, JavaScript (ES6+)
- **图表库**: Chart.js
- **文件导出**: FileSaver.js
- **数据存储**: localStorage
- **样式**: 响应式CSS Grid/Flexbox布局

## 使用方法

### 1. 基础使用
1. 打开 `index.html` 文件
2. 在"基础参数设置"区域输入投资参数：
   - 初始投资金额
   - 预期年增长率
   - 投资年限
   - 取款年限
   - 预期每年取款金额
3. 点击"计算"按钮查看结果

### 2. 高级调整
1. 完成基础计算后，切换到"逐年调整"标签页
2. 在表格中修改任意年份的：
   - 实际增长率
   - 实际取款金额（仅取款期）
   - 追加投资金额（仅投资期）
   - 备注信息
3. 点击"应用调整"按钮更新计算结果

### 3. 数据管理
1. 在"计算结果"页面点击"保存计算"
2. 输入计算名称并保存
3. 在"保存的计算"标签页中管理历史记录
4. 可以加载、删除单个记录或清除所有数据

### 4. 导出和导入
- **导出数据**：点击"导出数据"按钮，选择JSON格式（可导入）或文本格式（可读性好）
- **导入数据**：点击"导入数据"按钮，选择之前导出的JSON或文本文件
- **自动加载**：每次打开页面会自动加载上次的计算数据
- **打印功能**：点击"打印"按钮进行打印预览和打印

## 浏览器兼容性

- Chrome 60+
- Firefox 55+
- Safari 12+
- Edge 79+

## 注意事项

1. **数据安全**: 所有数据仅存储在用户本地浏览器中，不会上传到服务器
2. **计算精度**: 使用JavaScript原生数值计算，适用于一般投资规划场景
3. **存储限制**: localStorage有存储大小限制，建议定期清理不需要的计算记录
4. **网络要求**: 需要网络连接以加载Chart.js和FileSaver.js库

## 文件结构

```
高级复利计算器/
├── index.html          # 主页面文件
├── styles.css          # 样式文件
├── script.js           # 主要逻辑文件
├── README.md           # 说明文档
└── 高级复利计算器需求文档.md  # 需求文档
```

## 开发说明

项目采用原生Web技术开发，无需构建工具，可直接在浏览器中运行。代码结构清晰，易于维护和扩展。

### 主要类和方法

- `CompoundCalculator`: 主计算器类
- `performCalculation()`: 核心计算逻辑
- `updateDisplay()`: 更新界面显示
- `updateCharts()`: 更新图表
- `saveCalculation()`: 保存计算记录
- `exportData()`: 导出数据功能

## 许可证

本项目仅供学习和个人使用。

## 更新日志

### v1.0.0 (2024-12-28)
- 初始版本发布
- 实现所有核心功能
- 完成响应式设计
- 添加数据导出和打印功能
