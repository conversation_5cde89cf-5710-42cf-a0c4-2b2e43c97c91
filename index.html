<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>高级复利计算器</title>
    <link rel="stylesheet" href="styles.css">
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/file-saver@2.0.5/dist/FileSaver.min.js"></script>
</head>
<body>
    <div class="container">
        <header>
            <h1>高级复利计算器</h1>
            <p>精确计算复利增长，支持灵活的投资场景模拟</p>
        </header>

        <div class="input-section">
            <h2>基础参数设置</h2>
            <div class="input-grid">
                <div class="input-group">
                    <label for="initialAmount">初始投资金额 ($)</label>
                    <input type="number" id="initialAmount" min="0" step="0.01" value="10000">
                </div>
                <div class="input-group">
                    <label for="expectedGrowthRate">预期年增长率 (%)</label>
                    <input type="number" id="expectedGrowthRate" min="0" step="0.01" value="7">
                </div>
                <div class="input-group">
                    <label for="investmentYears">投资年限 (年)</label>
                    <input type="number" id="investmentYears" min="0" step="1" value="20">
                </div>
                <div class="input-group">
                    <label for="withdrawalYears">取款年限 (年)</label>
                    <input type="number" id="withdrawalYears" min="0" step="1" value="15">
                </div>
                <div class="input-group">
                    <label for="expectedWithdrawal">预期每年取款金额 ($)</label>
                    <input type="number" id="expectedWithdrawal" min="0" step="0.01" value="5000">
                </div>
            </div>
            <div class="button-group">
                <button id="calculateBtn" class="btn btn-primary">计算</button>
                <button id="resetBtn" class="btn btn-secondary">重置</button>
            </div>
        </div>

        <div class="tabs">
            <button class="tab-button active" data-tab="results">计算结果</button>
            <button class="tab-button" data-tab="adjustments">逐年调整</button>
            <button class="tab-button" data-tab="saved">保存的计算</button>
        </div>

        <div class="tab-content">
            <!-- 计算结果标签页 -->
            <div id="results" class="tab-panel active">
                <div class="summary-section">
                    <h3>摘要信息</h3>
                    <div class="summary-grid">
                        <div class="summary-item">
                            <span class="label">初始投资金额:</span>
                            <span id="summaryInitial" class="value">$0</span>
                        </div>
                        <div class="summary-item">
                            <span class="label">最终预期余额:</span>
                            <span id="summaryExpectedFinal" class="value">$0</span>
                        </div>
                        <div class="summary-item">
                            <span class="label">最终实际余额:</span>
                            <span id="summaryActualFinal" class="value">$0</span>
                        </div>
                        <div class="summary-item">
                            <span class="label">差异金额:</span>
                            <span id="summaryDifference" class="value">$0</span>
                        </div>
                        <div class="summary-item">
                            <span class="label">差异百分比:</span>
                            <span id="summaryDifferencePercent" class="value">0%</span>
                        </div>
                        <div class="summary-item">
                            <span class="label">总预期取款:</span>
                            <span id="summaryExpectedWithdrawals" class="value">$0</span>
                        </div>
                        <div class="summary-item">
                            <span class="label">总实际取款:</span>
                            <span id="summaryActualWithdrawals" class="value">$0</span>
                        </div>
                        <div class="summary-item">
                            <span class="label">总投入资金:</span>
                            <span id="summaryTotalInvestments" class="value">$0</span>
                        </div>
                        <div class="summary-item">
                            <span class="label">净现金流:</span>
                            <span id="summaryNetCashFlow" class="value">$0</span>
                        </div>
                    </div>
                </div>

                <div class="chart-section">
                    <div class="chart-header">
                        <h3>资产价值趋势</h3>
                        <div class="chart-controls">
                            <label for="assetChartTimeScale">时间维度：</label>
                            <select id="assetChartTimeScale" class="time-scale-select">
                                <option value="year">按年显示</option>
                                <option value="month">按月显示</option>
                            </select>
                        </div>
                    </div>
                    <div class="chart-container">
                        <canvas id="assetChart"></canvas>
                    </div>
                </div>

                <div class="chart-section">
                    <div class="chart-header">
                        <h3>取款金额对比</h3>
                        <div class="chart-controls">
                            <label for="withdrawalChartTimeScale">时间维度：</label>
                            <select id="withdrawalChartTimeScale" class="time-scale-select">
                                <option value="year">按年显示</option>
                                <option value="month">按月显示</option>
                            </select>
                        </div>
                    </div>
                    <div class="chart-container">
                        <canvas id="withdrawalChart"></canvas>
                    </div>
                </div>

                <div class="chart-section">
                    <div class="chart-header">
                        <h3>现金流分析</h3>
                        <div class="chart-controls">
                            <label for="cashFlowChartTimeScale">时间维度：</label>
                            <select id="cashFlowChartTimeScale" class="time-scale-select">
                                <option value="year">按年显示</option>
                                <option value="month">按月显示</option>
                            </select>
                        </div>
                    </div>
                    <div class="chart-container">
                        <canvas id="cashFlowChart"></canvas>
                    </div>
                </div>

                <div class="table-section">
                    <h3>详细数据表格</h3>
                    <div class="table-container">
                        <table id="resultsTable">
                            <thead>
                                <tr>
                                    <th>年份</th>
                                    <th>阶段</th>
                                    <th>预期增长率</th>
                                    <th>实际增长率</th>
                                    <th>预期资产价值</th>
                                    <th>实际资产价值</th>
                                    <th>预期取款</th>
                                    <th>实际取款</th>
                                    <th>差异</th>
                                    <th>备注</th>
                                </tr>
                            </thead>
                            <tbody>
                            </tbody>
                        </table>
                    </div>
                </div>

                <div class="action-section">
                    <button id="saveCalculationBtn" class="btn btn-primary">保存计算</button>
                    <button id="exportBtn" class="btn btn-secondary">导出数据</button>
                    <button id="importBtn" class="btn btn-secondary">导入数据</button>
                    <button id="printBtn" class="btn btn-secondary">打印</button>
                    <input type="file" id="importFile" accept=".txt,.json" style="display: none;">
                </div>
            </div>

            <!-- 逐年调整标签页 -->
            <div id="adjustments" class="tab-panel">
                <div class="adjustments-section">
                    <h3>逐月调整设置</h3>
                    <p>在此处可以调整每月的实际增长率、取款金额和追加投资。留空将使用默认值。</p>
                    <div class="info-box">
                        <h4>💡 计算说明</h4>
                        <ul>
                            <li><strong>按月精确计算</strong>：可以精确设置每个月的参数</li>
                            <li><strong>追加投资时机</strong>：在指定月份进行投资</li>
                            <li><strong>取款时机</strong>：在指定月份进行取款</li>
                            <li><strong>灵活设置</strong>：每个月都可以独立设置参数</li>
                        </ul>
                    </div>
                    <div class="month-filter">
                        <label for="yearSelect">选择年份：</label>
                        <select id="yearSelect">
                            <option value="all">显示所有年份</option>
                        </select>
                    </div>
                    <div class="table-container">
                        <table id="adjustmentsTable">
                            <thead>
                                <tr>
                                    <th>年份</th>
                                    <th>月份</th>
                                    <th>阶段</th>
                                    <th>实际月增长率 (%)</th>
                                    <th>取款金额 ($)</th>
                                    <th>追加投资 ($)</th>
                                    <th>备注</th>
                                </tr>
                            </thead>
                            <tbody>
                            </tbody>
                        </table>
                    </div>
                    <div class="button-group">
                        <button id="applyAdjustmentsBtn" class="btn btn-primary">应用调整</button>
                        <button id="clearAdjustmentsBtn" class="btn btn-secondary">清除调整</button>
                    </div>
                </div>
            </div>

            <!-- 保存的计算标签页 -->
            <div id="saved" class="tab-panel">
                <div class="saved-section">
                    <h3>保存的计算记录</h3>
                    <div id="savedCalculations">
                        <p class="no-data">暂无保存的计算记录</p>
                    </div>
                    <div class="button-group">
                        <button id="clearAllBtn" class="btn btn-danger">清除所有数据</button>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 模态框 -->
    <div id="saveModal" class="modal">
        <div class="modal-content">
            <span class="close">&times;</span>
            <h3>保存计算</h3>
            <div class="input-group">
                <label for="calculationName">计算名称:</label>
                <input type="text" id="calculationName" placeholder="输入计算名称">
            </div>
            <div class="button-group">
                <button id="confirmSaveBtn" class="btn btn-primary">保存</button>
                <button id="cancelSaveBtn" class="btn btn-secondary">取消</button>
            </div>
        </div>
    </div>

    <script src="script.js"></script>
</body>
</html>
