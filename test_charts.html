<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>图表功能测试</title>
    <link rel="stylesheet" href="styles.css">
    <script src="https://cdn.jsdelivr.net/npm/echarts@5.4.3/dist/echarts.min.js"></script>
</head>
<body>
    <div class="container">
        <h1>图表时间维度切换测试</h1>
        
        <div class="input-section">
            <h2>快速测试</h2>
            <button id="testBtn" class="btn btn-primary">生成测试数据</button>
            <button id="addMonthlyDataBtn" class="btn btn-secondary">添加月度现金流</button>
        </div>

        <div class="chart-section">
            <div class="chart-header">
                <h3>资产价值趋势</h3>
                <div class="chart-controls">
                    <label for="assetChartTimeScale">时间维度：</label>
                    <select id="assetChartTimeScale" class="time-scale-select">
                        <option value="year">按年显示</option>
                        <option value="month">按月显示</option>
                    </select>
                </div>
            </div>
            <div class="chart-container">
                <div id="assetChart" style="width: 100%; height: 400px;"></div>
            </div>
        </div>

        <div class="chart-section">
            <div class="chart-header">
                <h3>取款金额对比</h3>
                <div class="chart-controls">
                    <label for="withdrawalChartTimeScale">时间维度：</label>
                    <select id="withdrawalChartTimeScale" class="time-scale-select">
                        <option value="year">按年显示</option>
                        <option value="month">按月显示</option>
                    </select>
                </div>
            </div>
            <div class="chart-container">
                <div id="withdrawalChart" style="width: 100%; height: 400px;"></div>
            </div>
        </div>

        <div class="chart-section">
            <div class="chart-header">
                <h3>现金流分析</h3>
                <div class="chart-controls">
                    <label for="cashFlowChartTimeScale">时间维度：</label>
                    <select id="cashFlowChartTimeScale" class="time-scale-select">
                        <option value="year">按年显示</option>
                        <option value="month">按月显示</option>
                    </select>
                </div>
            </div>
            <div class="chart-container">
                <div id="cashFlowChart" style="width: 100%; height: 400px;"></div>
            </div>
        </div>
    </div>

    <script>
        // 简化的测试类
        class ChartTest {
            constructor() {
                this.charts = {};
                this.currentCalculation = null;
                this.init();
            }

            init() {
                document.getElementById('testBtn').addEventListener('click', () => this.generateTestData());
                document.getElementById('addMonthlyDataBtn').addEventListener('click', () => this.addMonthlyData());
                
                // 图表时间维度切换事件
                document.getElementById('assetChartTimeScale').addEventListener('change', () => {
                    if (this.currentCalculation) this.updateAssetChart();
                });
                
                document.getElementById('withdrawalChartTimeScale').addEventListener('change', () => {
                    if (this.currentCalculation) this.updateWithdrawalChart();
                });
                
                document.getElementById('cashFlowChartTimeScale').addEventListener('change', () => {
                    if (this.currentCalculation) this.updateCashFlowChart();
                });
            }

            generateTestData() {
                // 生成测试数据
                const yearlyData = [];
                const monthlyData = [];
                
                for (let year = 1; year <= 5; year++) {
                    const baseBalance = 10000 * Math.pow(1.12, year);
                    yearlyData.push({
                        year: year,
                        expectedBalance: baseBalance,
                        actualBalance: baseBalance * (0.95 + Math.random() * 0.1),
                        expectedWithdrawal: year > 3 ? 5000 : 0,
                        actualWithdrawal: year > 3 ? 4800 + Math.random() * 400 : 0,
                        additionalInvestment: year <= 3 ? 2000 : 0
                    });
                    
                    // 生成月度数据
                    for (let month = 1; month <= 12; month++) {
                        const monthBalance = baseBalance * (month / 12);
                        monthlyData.push({
                            year: year,
                            month: month,
                            expectedBalance: monthBalance,
                            actualBalance: monthBalance * (0.95 + Math.random() * 0.1),
                            withdrawal: 0,
                            additionalInvestment: 0
                        });
                    }
                }
                
                this.currentCalculation = { yearlyData, monthlyData };
                this.updateCharts();
            }

            addMonthlyData() {
                if (!this.currentCalculation) {
                    alert('请先生成测试数据');
                    return;
                }
                
                // 添加一些月度现金流
                this.currentCalculation.monthlyData[5].withdrawal = 1000;  // 第1年第6月取款
                this.currentCalculation.monthlyData[11].additionalInvestment = 3000; // 第1年第12月投资
                this.currentCalculation.monthlyData[17].withdrawal = 1500; // 第2年第6月取款
                this.currentCalculation.monthlyData[23].additionalInvestment = 2500; // 第2年第12月投资
                
                this.updateCharts();
            }

            updateCharts() {
                this.updateAssetChart();
                this.updateWithdrawalChart();
                this.updateCashFlowChart();
            }

            updateAssetChart() {
                const ctx = document.getElementById('assetChart').getContext('2d');
                const container = document.querySelector('#assetChart').closest('.chart-container');
                const timeScale = document.getElementById('assetChartTimeScale').value;

                if (this.charts.assetChart) {
                    this.charts.assetChart.destroy();
                }

                let labels, expectedBalances, actualBalances;
                
                if (timeScale === 'month') {
                    labels = this.currentCalculation.monthlyData.map(d => `${d.year}年${d.month}月`);
                    expectedBalances = this.currentCalculation.monthlyData.map(d => d.expectedBalance);
                    actualBalances = this.currentCalculation.monthlyData.map(d => d.actualBalance);
                    container.classList.add('month-view');
                } else {
                    labels = this.currentCalculation.yearlyData.map(d => `第${d.year}年`);
                    expectedBalances = this.currentCalculation.yearlyData.map(d => d.expectedBalance);
                    actualBalances = this.currentCalculation.yearlyData.map(d => d.actualBalance);
                    container.classList.remove('month-view');
                }

                this.charts.assetChart = new Chart(ctx, {
                    type: 'line',
                    data: {
                        labels: labels,
                        datasets: [{
                            label: '预期资产价值',
                            data: expectedBalances,
                            borderColor: '#4CAF50',
                            backgroundColor: 'rgba(76, 175, 80, 0.1)',
                            borderWidth: 2,
                            fill: false,
                            pointRadius: timeScale === 'month' ? 1 : 3
                        }, {
                            label: '实际资产价值',
                            data: actualBalances,
                            borderColor: '#2196F3',
                            backgroundColor: 'rgba(33, 150, 243, 0.1)',
                            borderWidth: 2,
                            fill: false,
                            pointRadius: timeScale === 'month' ? 1 : 3
                        }]
                    },
                    options: {
                        responsive: true,
                        maintainAspectRatio: false,
                        scales: {
                            x: {
                                ticks: {
                                    maxTicksLimit: timeScale === 'month' ? 24 : undefined,
                                    maxRotation: timeScale === 'month' ? 45 : 0
                                }
                            },
                            y: {
                                beginAtZero: true,
                                ticks: {
                                    callback: function(value) {
                                        return '¥' + value.toLocaleString();
                                    }
                                }
                            }
                        }
                    }
                });
            }

            updateWithdrawalChart() {
                const ctx = document.getElementById('withdrawalChart').getContext('2d');
                const container = document.querySelector('#withdrawalChart').closest('.chart-container');
                const timeScale = document.getElementById('withdrawalChartTimeScale').value;

                if (this.charts.withdrawalChart) {
                    this.charts.withdrawalChart.destroy();
                }

                let labels, data;
                
                if (timeScale === 'month') {
                    const monthlyWithdrawals = this.currentCalculation.monthlyData.filter(d => d.withdrawal > 0);
                    if (monthlyWithdrawals.length === 0) {
                        labels = ['暂无取款数据'];
                        data = [0];
                    } else {
                        labels = monthlyWithdrawals.map(d => `${d.year}年${d.month}月`);
                        data = monthlyWithdrawals.map(d => d.withdrawal);
                    }
                    container.classList.add('month-view');
                } else {
                    const yearlyWithdrawals = this.currentCalculation.yearlyData.filter(d => d.expectedWithdrawal > 0);
                    labels = yearlyWithdrawals.map(d => `第${d.year}年`);
                    data = yearlyWithdrawals;
                    container.classList.remove('month-view');
                }

                const datasets = timeScale === 'month' ? [{
                    label: '取款金额',
                    data: data,
                    backgroundColor: 'rgba(244, 67, 54, 0.7)',
                    borderColor: '#f44336',
                    borderWidth: 1
                }] : [{
                    label: '预期取款金额',
                    data: data.map(d => d.expectedWithdrawal),
                    backgroundColor: 'rgba(76, 175, 80, 0.7)',
                    borderColor: '#4CAF50',
                    borderWidth: 1
                }, {
                    label: '实际取款金额',
                    data: data.map(d => d.actualWithdrawal),
                    backgroundColor: 'rgba(33, 150, 243, 0.7)',
                    borderColor: '#2196F3',
                    borderWidth: 1
                }];

                this.charts.withdrawalChart = new Chart(ctx, {
                    type: 'bar',
                    data: { labels, datasets },
                    options: {
                        responsive: true,
                        maintainAspectRatio: false,
                        scales: {
                            x: { ticks: { maxRotation: timeScale === 'month' ? 45 : 0 } },
                            y: {
                                beginAtZero: true,
                                ticks: { callback: function(value) { return '¥' + value.toLocaleString(); } }
                            }
                        }
                    }
                });
            }

            updateCashFlowChart() {
                const ctx = document.getElementById('cashFlowChart').getContext('2d');
                const container = document.querySelector('#cashFlowChart').closest('.chart-container');
                const timeScale = document.getElementById('cashFlowChartTimeScale').value;

                if (this.charts.cashFlowChart) {
                    this.charts.cashFlowChart.destroy();
                }

                let labels, investments, withdrawals;
                
                if (timeScale === 'month') {
                    const monthlyData = this.currentCalculation.monthlyData.filter(d => 
                        d.additionalInvestment > 0 || d.withdrawal > 0
                    );
                    
                    if (monthlyData.length === 0) {
                        labels = ['暂无现金流数据'];
                        investments = [0];
                        withdrawals = [0];
                    } else {
                        labels = monthlyData.map(d => `${d.year}年${d.month}月`);
                        investments = monthlyData.map(d => d.additionalInvestment || 0);
                        withdrawals = monthlyData.map(d => -(d.withdrawal || 0));
                    }
                    container.classList.add('month-view');
                } else {
                    labels = this.currentCalculation.yearlyData.map(d => `第${d.year}年`);
                    investments = this.currentCalculation.yearlyData.map(d => d.additionalInvestment || 0);
                    withdrawals = this.currentCalculation.yearlyData.map(d => -(d.actualWithdrawal || 0));
                    container.classList.remove('month-view');
                }

                this.charts.cashFlowChart = new Chart(ctx, {
                    type: 'bar',
                    data: {
                        labels: labels,
                        datasets: [{
                            label: '追加投资',
                            data: investments,
                            backgroundColor: 'rgba(76, 175, 80, 0.7)',
                            borderColor: '#4CAF50',
                            borderWidth: 1
                        }, {
                            label: '取款',
                            data: withdrawals,
                            backgroundColor: 'rgba(244, 67, 54, 0.7)',
                            borderColor: '#f44336',
                            borderWidth: 1
                        }]
                    },
                    options: {
                        responsive: true,
                        maintainAspectRatio: false,
                        scales: {
                            x: { ticks: { maxRotation: timeScale === 'month' ? 45 : 0 } },
                            y: {
                                ticks: {
                                    callback: function(value) {
                                        return '¥' + Math.abs(value).toLocaleString();
                                    }
                                }
                            }
                        }
                    }
                });
            }
        }

        // 初始化测试
        new ChartTest();
    </script>
</body>
</html>
