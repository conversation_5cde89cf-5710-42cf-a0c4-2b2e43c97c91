/* 全局样式 */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    background-color: #f5f5f5;
    color: #333;
    line-height: 1.6;
}

.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 20px;
}

/* 头部样式 */
header {
    text-align: center;
    margin-bottom: 30px;
    padding: 20px;
    background: linear-gradient(135deg, #4CAF50, #2196F3);
    color: white;
    border-radius: 10px;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
}

header h1 {
    font-size: 2.5em;
    margin-bottom: 10px;
}

header p {
    font-size: 1.1em;
    opacity: 0.9;
}

/* 输入区域样式 */
.input-section {
    background: white;
    padding: 25px;
    border-radius: 10px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
    margin-bottom: 20px;
}

.input-section h2 {
    color: #4CAF50;
    margin-bottom: 20px;
    font-size: 1.5em;
}

.input-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 20px;
    margin-bottom: 25px;
}

.input-group {
    display: flex;
    flex-direction: column;
}

.input-group label {
    font-weight: 600;
    margin-bottom: 5px;
    color: #555;
}

.input-group input {
    padding: 12px;
    border: 2px solid #ddd;
    border-radius: 5px;
    font-size: 16px;
    transition: border-color 0.3s ease;
}

.input-group input:focus {
    outline: none;
    border-color: #4CAF50;
    box-shadow: 0 0 0 3px rgba(76, 175, 80, 0.1);
}

/* 按钮样式 */
.button-group {
    display: flex;
    gap: 10px;
    flex-wrap: wrap;
}

.btn {
    padding: 12px 24px;
    border: none;
    border-radius: 5px;
    font-size: 16px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    text-decoration: none;
    display: inline-block;
    text-align: center;
}

.btn-primary {
    background-color: #4CAF50;
    color: white;
}

.btn-primary:hover {
    background-color: #45a049;
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
}

.btn-secondary {
    background-color: #2196F3;
    color: white;
}

.btn-secondary:hover {
    background-color: #1976D2;
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
}

.btn-danger {
    background-color: #f44336;
    color: white;
}

.btn-danger:hover {
    background-color: #d32f2f;
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
}

/* 标签页样式 */
.tabs {
    display: flex;
    background: white;
    border-radius: 10px 10px 0 0;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
    overflow: hidden;
}

.tab-button {
    flex: 1;
    padding: 15px 20px;
    border: none;
    background: #f8f9fa;
    color: #666;
    font-size: 16px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    border-bottom: 3px solid transparent;
}

.tab-button:hover {
    background: #e9ecef;
    color: #333;
}

.tab-button.active {
    background: white;
    color: #4CAF50;
    border-bottom-color: #4CAF50;
}

.tab-content {
    background: white;
    border-radius: 0 0 10px 10px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
    min-height: 400px;
}

.tab-panel {
    display: none;
    padding: 25px;
}

.tab-panel.active {
    display: block;
}

/* 摘要信息样式 */
.summary-section {
    margin-bottom: 30px;
}

.summary-section h3 {
    color: #4CAF50;
    margin-bottom: 15px;
    font-size: 1.3em;
}

.summary-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 15px;
}

.summary-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 12px 15px;
    background: #f8f9fa;
    border-radius: 5px;
    border-left: 4px solid #4CAF50;
}

.summary-item .label {
    font-weight: 600;
    color: #555;
}

.summary-item .value {
    font-weight: 700;
    color: #333;
    font-size: 1.1em;
}

/* 图表样式 */
.chart-section {
    margin-bottom: 30px;
}

.chart-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 15px;
    flex-wrap: wrap;
    gap: 10px;
}

.chart-section h3 {
    color: #4CAF50;
    margin: 0;
    font-size: 1.3em;
}

.chart-controls {
    display: flex;
    align-items: center;
    gap: 8px;
}

.chart-controls label {
    font-size: 0.9em;
    color: #666;
    white-space: nowrap;
}

.time-scale-select {
    padding: 4px 8px;
    border: 1px solid #ddd;
    border-radius: 4px;
    font-size: 0.9em;
    background-color: white;
    cursor: pointer;
}

.time-scale-select:focus {
    outline: none;
    border-color: #4CAF50;
    box-shadow: 0 0 0 2px rgba(76, 175, 80, 0.2);
}

.chart-container {
    position: relative;
    overflow-x: auto;
    overflow-y: hidden;
    border: 1px solid #e0e0e0;
    border-radius: 8px;
    background-color: #fafafa;
    padding: 10px;
}

.chart-container.scrollable {
    min-width: 100%;
}

.chart-container canvas {
    max-height: 400px;
    height: 400px !important;
    display: block;
}

/* 月度视图时的特殊样式 */
.chart-container.month-view {
    overflow-x: auto;
    padding-bottom: 15px;
}

.chart-container.month-view canvas {
    min-width: 1200px;
    width: auto !important;
}

/* 滚动条样式 */
.chart-container::-webkit-scrollbar {
    height: 8px;
}

.chart-container::-webkit-scrollbar-track {
    background: #f1f1f1;
    border-radius: 4px;
}

.chart-container::-webkit-scrollbar-thumb {
    background: #c1c1c1;
    border-radius: 4px;
}

.chart-container::-webkit-scrollbar-thumb:hover {
    background: #a8a8a8;
}

/* 响应式设计 */
@media (max-width: 768px) {
    .chart-header {
        flex-direction: column;
        align-items: flex-start;
        gap: 15px;
    }

    .chart-controls {
        width: 100%;
        justify-content: space-between;
    }

    .chart-container.month-view canvas {
        min-width: 800px;
    }

    .time-scale-select {
        flex: 1;
        max-width: 150px;
    }
}

/* 表格样式 */
.table-section {
    margin-bottom: 30px;
}

.table-section h3 {
    color: #4CAF50;
    margin-bottom: 15px;
    font-size: 1.3em;
}

.table-container {
    overflow-x: auto;
    border-radius: 5px;
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
}

table {
    width: 100%;
    border-collapse: collapse;
    background: white;
    font-size: 14px;
}

table th,
table td {
    padding: 12px 8px;
    text-align: left;
    border-bottom: 1px solid #ddd;
}

table th {
    background-color: #4CAF50;
    color: white;
    font-weight: 600;
    position: sticky;
    top: 0;
    z-index: 10;
}

table tr:hover {
    background-color: #f5f5f5;
}

table td input {
    width: 100%;
    padding: 6px;
    border: 1px solid #ddd;
    border-radius: 3px;
    font-size: 13px;
}

table td input:focus {
    outline: none;
    border-color: #4CAF50;
}

/* 数值颜色样式 */
.positive {
    color: #4CAF50;
    font-weight: 600;
}

.negative {
    color: #f44336;
    font-weight: 600;
}

/* 阶段标识样式 */
.phase-investment {
    background-color: #e8f5e8;
    color: #2e7d32;
    padding: 2px 8px;
    border-radius: 12px;
    font-size: 12px;
    font-weight: 600;
}

.phase-withdrawal {
    background-color: #e3f2fd;
    color: #1565c0;
    padding: 2px 8px;
    border-radius: 12px;
    font-size: 12px;
    font-weight: 600;
}

/* 操作区域样式 */
.action-section {
    border-top: 1px solid #eee;
    padding-top: 20px;
}

/* 调整区域样式 */
.adjustments-section {
    margin-bottom: 20px;
}

.adjustments-section h3 {
    color: #4CAF50;
    margin-bottom: 10px;
    font-size: 1.3em;
}

.adjustments-section p {
    color: #666;
    margin-bottom: 20px;
}

/* 信息框样式 */
.info-box {
    background: #e8f5e8;
    border: 1px solid #c8e6c9;
    border-radius: 8px;
    padding: 15px;
    margin: 15px 0;
}

.info-box h4 {
    color: #2e7d32;
    margin: 0 0 10px 0;
    font-size: 1.1em;
}

.info-box ul {
    margin: 0;
    padding-left: 20px;
    color: #2e7d32;
}

.info-box li {
    margin-bottom: 5px;
    line-height: 1.4;
}

.info-box strong {
    color: #1b5e20;
}

/* 保存区域样式 */
.saved-section h3 {
    color: #4CAF50;
    margin-bottom: 20px;
    font-size: 1.3em;
}

.saved-calculation {
    background: #f8f9fa;
    padding: 15px;
    border-radius: 5px;
    margin-bottom: 10px;
    border-left: 4px solid #2196F3;
}

.saved-calculation h4 {
    color: #333;
    margin-bottom: 5px;
}

.saved-calculation p {
    color: #666;
    font-size: 14px;
    margin-bottom: 10px;
}

.saved-calculation .button-group {
    gap: 5px;
}

.saved-calculation .btn {
    padding: 6px 12px;
    font-size: 14px;
}

.no-data {
    text-align: center;
    color: #999;
    font-style: italic;
    padding: 40px;
}

/* 模态框样式 */
.modal {
    display: none;
    position: fixed;
    z-index: 1000;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.5);
}

.modal-content {
    background-color: white;
    margin: 15% auto;
    padding: 25px;
    border-radius: 10px;
    width: 90%;
    max-width: 500px;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.3);
}

.close {
    color: #aaa;
    float: right;
    font-size: 28px;
    font-weight: bold;
    cursor: pointer;
    line-height: 1;
}

.close:hover {
    color: #333;
}

.modal h3 {
    color: #4CAF50;
    margin-bottom: 20px;
}

/* 响应式设计 */
@media (max-width: 768px) {
    .container {
        padding: 10px;
    }
    
    header h1 {
        font-size: 2em;
    }
    
    .input-grid {
        grid-template-columns: 1fr;
    }
    
    .tabs {
        flex-direction: column;
    }
    
    .tab-button {
        border-bottom: 1px solid #ddd;
        border-radius: 0;
    }
    
    .tab-button.active {
        border-bottom-color: #4CAF50;
    }
    
    .summary-grid {
        grid-template-columns: 1fr;
    }
    
    .button-group {
        flex-direction: column;
    }
    
    .btn {
        width: 100%;
    }
}

/* 打印样式 */
@media print {
    body {
        background: white;
    }
    
    .container {
        max-width: none;
        padding: 0;
    }
    
    .tabs,
    .action-section,
    .button-group,
    .btn {
        display: none !important;
    }
    
    .tab-panel {
        display: block !important;
        padding: 0;
    }
    
    .chart-section {
        page-break-inside: avoid;
    }
    
    table {
        font-size: 12px;
    }
    
    table th,
    table td {
        padding: 8px 4px;
    }
}

.month-filter {
    margin-bottom: 20px;
    padding: 15px;
    background: #f8f9fa;
    border-radius: 8px;
    border: 1px solid #e9ecef;
}

.month-filter label {
    font-weight: 600;
    margin-right: 10px;
    color: #495057;
}

.month-filter select {
    padding: 8px 12px;
    border: 1px solid #ced4da;
    border-radius: 4px;
    background: white;
    font-size: 14px;
    min-width: 150px;
}

.month-filter select:focus {
    outline: none;
    border-color: #007bff;
    box-shadow: 0 0 0 2px rgba(0, 123, 255, 0.25);
}

#adjustmentsTable th:nth-child(2) {
    width: 80px;
}

#adjustmentsTable td:nth-child(2) {
    text-align: center;
    font-weight: 600;
    color: #6c757d;
}
